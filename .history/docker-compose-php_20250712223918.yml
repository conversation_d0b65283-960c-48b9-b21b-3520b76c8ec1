version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: php-nginx
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./:/var/www/html
    depends_on:
      - php-fpm
    networks:
      - php-network
    # Run as specific user (optional)
    user: "1000:1000"  # Replace with your UID:GID

  php-fpm:
    image: php:8.2-fpm
    container_name: php-fpm
    volumes:
      - ./:/var/www/html
      - ./php-fpm-pool.conf:/usr/local/etc/php-fpm.d/www.conf
    networks:
      - php-network
    environment:
      - PHP_FPM_USER=www-data
      - PHP_FPM_GROUP=www-data

networks:
  php-network:
    driver: bridge
