// function core(a,b,c) {
//     console.log('core logic', a, b, c);
// }

// Function.prototype.before = function (fn) {
//     return (...args) => {
//         fn();
//         this(...args);
//     }
// }

// const newCore = core.before(() => {
//     console.log('before core');
// });

// newCore(1,2,3);

// const helloType = Object.prototype.toString.call('hello');

const extensions = {
    '.js'() {
        console.log('js');
    },
    '.css'() {
        console.log('css');
    },
    '.html'() {
        console.log('html');
    }
};

extensions['.js']();

'.ab' () {
    console.log('ab');
}

'.ab'();