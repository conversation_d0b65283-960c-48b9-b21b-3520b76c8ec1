<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: roomservice.proto

namespace RoomService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>roomservice.Meal</code>
 */
class Meal extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.roomservice.Entree entree = 1;</code>
     */
    protected $entree = null;
    /**
     * Generated from protobuf field <code>.roomservice.Drink drink = 2;</code>
     */
    protected $drink = null;
    /**
     * Generated from protobuf field <code>repeated .roomservice.Side side = 3;</code>
     */
    private $side;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \RoomService\Entree $entree
     *     @type \RoomService\Drink $drink
     *     @type array<\RoomService\Side>|\Google\Protobuf\Internal\RepeatedField $side
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Roomservice::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.roomservice.Entree entree = 1;</code>
     * @return \RoomService\Entree|null
     */
    public function getEntree()
    {
        return $this->entree;
    }

    public function hasEntree()
    {
        return isset($this->entree);
    }

    public function clearEntree()
    {
        unset($this->entree);
    }

    /**
     * Generated from protobuf field <code>.roomservice.Entree entree = 1;</code>
     * @param \RoomService\Entree $var
     * @return $this
     */
    public function setEntree($var)
    {
        GPBUtil::checkMessage($var, \RoomService\Entree::class);
        $this->entree = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.roomservice.Drink drink = 2;</code>
     * @return \RoomService\Drink|null
     */
    public function getDrink()
    {
        return $this->drink;
    }

    public function hasDrink()
    {
        return isset($this->drink);
    }

    public function clearDrink()
    {
        unset($this->drink);
    }

    /**
     * Generated from protobuf field <code>.roomservice.Drink drink = 2;</code>
     * @param \RoomService\Drink $var
     * @return $this
     */
    public function setDrink($var)
    {
        GPBUtil::checkMessage($var, \RoomService\Drink::class);
        $this->drink = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>repeated .roomservice.Side side = 3;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getSide()
    {
        return $this->side;
    }

    /**
     * Generated from protobuf field <code>repeated .roomservice.Side side = 3;</code>
     * @param array<\RoomService\Side>|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setSide($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \RoomService\Side::class);
        $this->side = $arr;

        return $this;
    }

}

