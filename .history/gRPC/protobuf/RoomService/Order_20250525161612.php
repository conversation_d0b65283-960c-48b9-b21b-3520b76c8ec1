<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: roomservice.proto

namespace RoomService;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>roomservice.Order</code>
 */
class Order extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>.google.protobuf.Timestamp order_time = 1;</code>
     */
    protected $order_time = null;
    /**
     * Generated from protobuf field <code>float cost = 2;</code>
     */
    protected $cost = 0.0;
    /**
     * Generated from protobuf field <code>map<string, .roomservice.Meal> meals = 3;</code>
     */
    private $meals;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Protobuf\Timestamp $order_time
     *     @type float $cost
     *     @type array|\Google\Protobuf\Internal\MapField $meals
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Roomservice::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Timestamp order_time = 1;</code>
     * @return \Google\Protobuf\Timestamp|null
     */
    public function getOrderTime()
    {
        return $this->order_time;
    }

    public function hasOrderTime()
    {
        return isset($this->order_time);
    }

    public function clearOrderTime()
    {
        unset($this->order_time);
    }

    /**
     * Generated from protobuf field <code>.google.protobuf.Timestamp order_time = 1;</code>
     * @param \Google\Protobuf\Timestamp $var
     * @return $this
     */
    public function setOrderTime($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Timestamp::class);
        $this->order_time = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>float cost = 2;</code>
     * @return float
     */
    public function getCost()
    {
        return $this->cost;
    }

    /**
     * Generated from protobuf field <code>float cost = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setCost($var)
    {
        GPBUtil::checkFloat($var);
        $this->cost = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>map<string, .roomservice.Meal> meals = 3;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getMeals()
    {
        return $this->meals;
    }

    /**
     * Generated from protobuf field <code>map<string, .roomservice.Meal> meals = 3;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setMeals($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::STRING, \Google\Protobuf\Internal\GPBType::MESSAGE, \RoomService\Meal::class);
        $this->meals = $arr;

        return $this;
    }

}

