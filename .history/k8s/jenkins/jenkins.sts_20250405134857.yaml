apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: jenkins
  namespace: jenkins
spec:
  selector:
    matchLabels:
      app: jenkins
  serviceName: jenkins
  replicas: 1
  template:
    metadata:
      labels:
        app: jenkins
    spec:
      containers:
      - name: jenkins
        image: jenkins/jenkins:lts
        ports:
        - containerPort: 8080
          name: web
        - containerPort: 50000
          name: agent
        volumeMounts:
        - name: www
          mountPath: /var/jenkins_home
  volumeClaimTemplates:
  - metadata:
      name: jenkins_home
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: retained
      resources:
        requests:
          storage: 2Gi
