podTemplate(label: 'jenkins-slave', containers: [
    containerTemplate(name: 'jnlp', image: 'jenkins/inbound-agent:latest')
]) {
    node(POD_LABEL) {
        stage('Example Build') {
            echo "Hello, <PERSON>!"
        }
    }
}


pipeline {
    agent {
      kubernetes {
        yaml '''spec:
      containers:
      - name: maven
        image: maven
        command:
        - sleep
        args:
        - infinity'''
      }
    }


    stages {
        stage('Hello') {
            steps {
                container('maven') {
                    sh 'mvn -version'
                }

                echo 'Started'
                sleep 2
                echo 'Half way'
                sleep 2
                echo 'Ended'
            }
        }
    }
}
