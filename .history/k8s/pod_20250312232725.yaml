apiVersion: v1
kind: Pod
metadata:
  name: nginx-demo
  labels:
    app: nginx-demo
    env: dev
spec:
  containers:
  - name: nginx-demo
    image: nginx
    resources:
      limits:
        memory: "128Mi"
        cpu: "500m"
    ports:
      - containerPort: 80
        hostPort: 8090
    volumeMounts:
      - mountPath: "/usr/share/nginx/html"
        name: nginx-data
  volumes:
  - name: nginx-data
    persistentVolumeClaim:
      claimName: pv-claim

