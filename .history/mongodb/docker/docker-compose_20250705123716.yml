services:
  mongodb:
    image: mongo:7.0
    container_name: mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_DATABASE: myapp
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - mongodb_network

  # Optional: MongoDB Express for web-based admin interface
  mongo-express:
    image: mongo-express:1.0.0
    container_name: mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_URL: mongodb://mongodb:27017/
      ME_CONFIG_BASICAUTH: false
    depends_on:
      - mongodb
    networks:
      - mongodb_network

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local

networks:
  mongodb_network:
    driver: bridge
