### list
GET  http://************:3000/books HTTP/1.1
Host: localhost:3000
Accept: */*
Origin: http://localhost:3000
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.186 Safari/537.36
Content-Type: application/json

### create
POST http://************:3000/books HTTP/1.1
Host: localhost:3000
Accept: */*
Origin: http://localhost:3000
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.186 Safari/537.36
Content-Type: application/json

{
    "title": "Book Title2",
    "author": "Author Name2",
    "rating": 9,
    "genres": ["Fiction", "magic"],
    "reviews": [
        {
            "name": "review 11",
            "review": "This is a great book!"
        },
        {
            "name": "review 22",
            "review": "I liked it but not as much as I thought I would."
        }
    ]
}

### delete
DELETE http://************:3000/books/67632f4df1f1f4975a0272ee HTTP/1.1
Host: localhost:3000
Accept: */*
Origin: http://localhost:3000
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.186 Safari/537.36
Content-Type: application/json


### patch
PATCH http://localhost:3000/books/6763528dafb3cd1f7e5b23cc HTTP/1.1
Host: localhost:3000
Accept: */*
Origin: http://localhost:3000
User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/64.0.3282.186 Safari/537.36
Content-Type: application/json

{
    "reviews": [
        {
            "name": "review 11",
            "review": "This is a great book!"
        },
        {
            "name": "review 22",
            "review": "I liked it but not as much as I thought I would."
        }
    ]
}
