const { MongoClient } = require('mongodb');

let db;

module.exports = {
    connectToDb: (cb) => {
        MongoClient.connect('mongodb://localhost:27017/bookstore')
            .then((client) => {
                db = client.db('bookstore');
                console.log("Connected to test database");
                return cb()
            }).catch(err => {
                console.error(err);
                cb(err);
            });
    },

    getDb: () => db
};