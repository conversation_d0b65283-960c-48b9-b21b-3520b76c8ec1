services:
    dev:
        container_name: dev_container
        build:
            context: '.'
            dockerfile: Dockerfile
            args:
                WWWGROUP: '${WWWGROUP}'
                WWWUSER: '${WWWUSER}'
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '80:80'
            - '3000:3000'
        volumes:
            - '.:/home/<USER>/workspace'
            - './nginx.conf:/etc/nginx/conf.d/nginx.conf'


