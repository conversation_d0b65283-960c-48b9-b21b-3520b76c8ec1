- name: Install apache
  hosts: vm62
  become: true

  vars:
   student_name: "<PERSON><PERSON>"

  tasks:
    - name: Install apache2
      package:
        name: apache2
        state: present

    - name: Create index.html
      copy:
        dest: /var/www/localhost/htdocs/index.html
        content: "<h1>Hello {{ student_name }}!</h1>"

    - name: Start apache2 service
      service:
        name: apache2
        state: started
        enabled: true
