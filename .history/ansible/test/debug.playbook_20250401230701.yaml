- name: Install apache
  hosts: vm62
  become: true

  vars:
   student_name: "<PERSON><PERSON>"

  tasks:
    - name: print student name
      debug:
        msg: "Hello {{ student_name }}!"

    - name: Print names
      debug:
        msg: "{{ item }}"
      loop:
        - "<PERSON><PERSON>"
        - "<PERSON>"
        - "<PERSON><PERSON>"
        - "<PERSON>"
        - "<PERSON>"

    - name: Print distribution
      debug:
        msg: "{{ ansible_facts.distribution }}"
